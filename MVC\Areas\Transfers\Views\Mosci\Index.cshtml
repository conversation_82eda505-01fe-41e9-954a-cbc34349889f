@model Odrdc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel

@{
    ViewBag.Title = "MOSCI";
}




@if (!string.IsNullOrWhiteSpace(ViewBag.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(ViewBag.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @ViewBag.ErrorMessage
    </div>
}

@if (!string.IsNullOrWhiteSpace(Model.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(Model.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.ErrorMessage
    </div>
}





<div id="divErrorMessage" class="alert alert-danger fade in" style="display:none">
    <a href="#" class="close" data-dismiss="alert" aria-label="close">X</a>
    <span id="ErrorMessage"></span>
</div>

@* Hidden fields for search functionality *@
@Html.HiddenFor(m => m.SearchPrefix)
@Html.HiddenFor(m => m.SearchOffenderId)

@*<div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-12">
                <div class="divFindOffender">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Find Inmate
                        </div>
                        <div class="panel-body">
                            <div class="form-inline">
                                <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                    @Html.DropDownListFor(m => m.InmateIdPrefix, Model.Prefix, new { @class = "form-control" })
                                    @Html.TextBoxFor(m => m.OffenderId, new { @class = "form-control onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6", Value = string.Empty })
                                    <button id="btnFindOffender" type="button" class="btn btn-primary" name="submitAction" value="Search">
                                        <span>Find Inmate</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@

@*<div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-4">
                <select name="InmateIdPrefix" class="form-control input-sm" style="display:inline;width:80px;">
                    @foreach (var item in Model.Prefix)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
                <input type="text" name="OffenderId" class="form-control input-sm" placeholder="Offender #" style="display:inline;width:120px;" />
                <button type="button" class="btn btn-success" id="btnFindOffender">
                    <span class="glyphicon glyphicon-search"></span> Find Inmate
                </button>
            </div>
        </div>
    </div>
    <br />*@
@using (Html.BeginForm("Mosci", "Mosci", new { area = "Transfers" }, FormMethod.Post, new { @id = "Mosci", @class = "form-horizontal" }))
{
    @Html.AntiForgeryToken()

    @* Hidden field for JSON model data (used by JavaScript) *@
    @Html.Hidden("modelJson", "", new { id = "modelJson" })

    @* Hidden fields for auto-population functionality *@
    @Html.Hidden("autoPopulateRowIndex", "", new { id = "autoPopulateRowIndex" })

    <div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-12">
                <div class="divFindOffender">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Find Inmate
                        </div>
                        <div class="panel-body">
                            <div class="form-inline">
                                <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                    @Html.DropDownListFor(m => m.SearchPrefix, Model.PrefixOptions, new { @class = "form-control input-sm", @id = "searchPrefixDropdown" })
                                    @Html.TextBoxFor(m => m.SearchOffenderId, new { @class = "form-control input-sm onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6" })
                                    @* <button id="btnFindOffender" type="button" class="btn btn-primary" name="submitAction" value="Search">
                                            <span>Find Inmate</span>
                                        </button> *@
                                    <button type="submit" class="btn btn-primary" name="submitAction" value="Search">
                                        <span>Find Inmate</span>
                                    </button>
                                    @*<button type="submit" name="submitAction" value="Search" class="btn btn-info" title="Server-side search">
                                            <span class="glyphicon glyphicon-search"></span> Server Search
                                        </button>*@
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    //-------------------------------------------------
    <div class="panel panel-primary">
        <div class="panel-heading">
            Schedule Inmate Move&nbsp;&nbsp;-&nbsp;&nbsp;MOSCI
        </div>
        <div class="panel-body">
            <div class="table-responsive">
                <table id="inmateTable" class="table table-bordered table-condensed">
                    <thead class="odrc-header-row">
                        <tr>
                            <td style="width:60px;">Prefix</td>
                            <td style="width:120px;">Offender #</td>
                            <td style="width:150px;">Last Name</td>
                            <td style="width:150px;">First Name</td>
                            <td style="width:120px;">From</td>
                            <td style="width:140px;">To</td>
                            <td style="width:140px;">Scheduled Date</td>
                            <td style="width:80px;">Comments</td>
                            <td style="width:80px;">Remove</td>
                            <td style="width:80px;">Delete</td>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < Model.Inmates.Count; i++)
                        {
                            <tr @if (i == 0) { <text> id="inmate-row-template" </text> }>
                                <td>
                                    @Html.DropDownListFor(m => m.Inmates[i].InmateIdPrefix, Model.PrefixOptions, new { @class = "form-control input-sm" })
                                    @Html.HiddenFor(m => m.Inmates[i].Recno)
                                    @Html.HiddenFor(m => m.Inmates[i].OffenderId)
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].OffenderId, new { @class = "form-control input-sm onlyNumeric auto-populate-field", @maxlength = "6" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].LastName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].FirstName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                <td>
                                    @*<select name="FromInstitution" class="form-control input-sm">
                <option>MANCINI</option>

            </select>*@
                                    @{

                                        var frominsText = "";
                                        if (Model.Inmates[i].Instno.HasValue)
                                        {
                                            var fromOption = Model.InstnoDrp.FirstOrDefault(x => x.Value == Model.Inmates[i].Instno.ToString());
                                            frominsText = fromOption?.Text ?? "";
                                        }
                                    }

                                    @Html.TextBoxFor(m => m.Inmates[i].Instno, new { @class = "form-control input-sm", @readonly = "readonly", @style = "display:none;" })
                                    @Html.TextBox("frominsText", frominsText, new { @class = "form-control input-sm", @readonly = "readonly" })
                                </td>
                                @*<td>@Html.TextBoxFor(m => m.Inmates[i].SchdInst, new { @class = "form-control input-sm" })</td>*@
                                <td>
                                    @{
                                        var item = Model.SchdInstDrp
                                            .Select(x => new SelectListItem
                                            {
                                                Text = x.Text,
                                                Value = x.Value,
                                                Selected = (x.Value == Model.Inmates[i].SchdInst.ToString())

                                            });
                                    }
                                    @Html.DropDownListFor(m => m.Inmates[i].SchdInst, item, new { @class = "form-control input-sm" })

                                    @*@Html.DropDownListFor(m => m.Inmates[i].SchdInst, Model.SchdInstDrp,"Select", new { @class = "form-control input-sm" })*@

                                </td>
                                @*<td>
                                @Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm" })
                                <span class="glyphicon glyphicon-calendar"></span>
                            </td>*@
                                                    @*<td>
                                <div class="input-group scheduled-date">
                                    @Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm", @autocomplete = "off", @placeholder = "MM/DD/YYYY", @readonly = "readonly" })
                                    <span class="input-group-addon date-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </td>*@
                                <td>
                                    <div class="input-group input-group-sm">
                                        @Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm datepicker-input" })
                                        <span class="input-group-addon datepicker-trigger" style="cursor: pointer;">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].Descrl, new { @class = "form-control input-sm" })</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForRemoval)</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForDeletion)</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Add / Remove Row Buttons -->
            <div class="row">
                <div class="col-md-6 col-xs-12">
                    @*<button type="submit" name="submitAction" value="AddNew" id="btnAddNewInmate" class="btn btn-primary">
                            <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                        </button>*@
                    <button type="submit" id="btnAddNewInmate" class="btn btn-primary">
                        <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                    </button>
                </div>
                <div class="col-md-6 col-xs-12 text-right">
                    <button type="submit" name="submitAction" value="RemoveSelected" name="btnRemoveInmate" class="btn btn-danger">
                        <span class="glyphicon glyphicon-remove"></span> Remove Inmate
                    </button>
                    <span style="display:inline-block; width:20px;"></span>
                    <button type="submit" name="submitAction" value="DeleteSelected" id="btnDelete" class="btn btn-default">
                        <span class="glyphicon glyphicon-trash"></span> Delete
                    </button>
                </div>
            </div>
            <br />
            <!-- Save / Cancel Buttons -->
            <div class="row text-center">
                <button type="submit" name="submitAction" value="Save" class="btn btn-primary" id="btnSave">
                    <span class="glyphicon glyphicon-floppy-disk"></span> Save
                </button>
                <button type="submit" name="submitAction" value="Cancel" class="btn btn-default" id="btnCancel">
                    <span class="glyphicon glyphicon-remove-circle"></span> Cancel
                </button>
            </div>
        </div>
    </div>


    @section PageCss
{
        <link href="~/Areas/Rh/Content/CountOffice/jquery-ui.css" rel="stylesheet" />
        <link href="~/Areas/Rh/Content/CountOffice/jquery-ui.theme.css" rel="stylesheet" />

        <style>
            /* Date picker styling */
            .datepicker-trigger {
                background-color: #f5f5f5;
                border-left: 1px solid #ccc;
                padding: 5px 8px;
            }

                .datepicker-trigger:hover {
                    background-color: #e8e8e8;
                }

                .datepicker-trigger .glyphicon-calendar {
                    color: #337ab7;
                    font-size: 12px;
                }

            /* Ensure input group sizing is consistent */
            .input-group-sm .input-group-addon {
                padding: 5px 8px;
                font-size: 12px;
                line-height: 1.5;
                border-radius: 3px;
            }

            /* Custom datepicker styling to match the expected result */
            .ui-datepicker {
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                font-size: 11px;
                width: 200px;
                border: 1px solid #999;
                background: white;
                box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
                padding: 0;
            }

            /* Validation error styling */
            .validation-error {
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                color: #721c24;
                padding: 8px 12px;
                border-radius: 4px;
                margin: 5px 0;
                font-size: 14px;
            }

            .validation-error .close {
                color: #721c24;
                opacity: 0.8;
            }

            .validation-error .close:hover {
                opacity: 1;
            }

                .ui-datepicker .ui-datepicker-header {
                    background: #f0f0f0;
                    border: none;
                    border-bottom: 1px solid #ccc;
                    color: #333;
                    font-weight: normal;
                    padding: 4px 8px;
                    position: relative;
                    height: 20px;
                }

                .ui-datepicker .ui-datepicker-prev,
                .ui-datepicker .ui-datepicker-next {
                    position: absolute;
                    top: 2px;
                    width: 16px;
                    height: 16px;
                    background: #f0f0f0;
                    border: 1px solid #999;
                    cursor: pointer;
                    text-align: center;
                    line-height: 14px;
                    font-size: 10px;
                    color: #333;
                }

                .ui-datepicker .ui-datepicker-prev {
                    left: 4px;
                }

                .ui-datepicker .ui-datepicker-next {
                    right: 4px;
                }

                    .ui-datepicker .ui-datepicker-prev:hover,
                    .ui-datepicker .ui-datepicker-next:hover {
                        background: #e0e0e0;
                    }

                .ui-datepicker .ui-datepicker-title {
                    line-height: 20px;
                    margin: 0 25px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 11px;
                }

                /* Style the month and year dropdowns */
                .ui-datepicker .ui-datepicker-month,
                .ui-datepicker .ui-datepicker-year {
                    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                    font-size: 11px;
                    border: 1px solid #999;
                    background: white;
                    padding: 1px 2px;
                    margin: 0 2px;
                    color: #333;
                }

                .ui-datepicker .ui-datepicker-month {
                    width: 70px;
                }

                .ui-datepicker .ui-datepicker-year {
                    width: 50px;
                }

                .ui-datepicker table {
                    width: 100%;
                    font-size: 11px;
                    border-collapse: collapse;
                    margin: 0;
                    border-spacing: 0;
                }

                .ui-datepicker th {
                    padding: 4px 2px;
                    text-align: center;
                    font-weight: normal;
                    border: none;
                    background: #f8f8f8;
                    color: #666;
                    font-size: 10px;
                    border-bottom: 1px solid #ddd;
                }

                .ui-datepicker td {
                    border: none;
                    padding: 0;
                    text-align: center;
                }

                    .ui-datepicker td span,
                    .ui-datepicker td a {
                        display: block;
                        padding: 2px;
                        text-align: center;
                        text-decoration: none;
                        border: 1px solid transparent;
                        font-size: 11px;
                        color: #333;
                        width: 20px;
                        height: 16px;
                        line-height: 16px;
                        margin: 1px;
                    }

                        .ui-datepicker td a:hover {
                            background: #316AC5;
                            color: white;
                            border: 1px solid #316AC5;
                        }

                .ui-datepicker .ui-datepicker-today a {
                    background: #316AC5;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #316AC5;
                }

                .ui-datepicker .ui-datepicker-current-day a {
                    background: #316AC5;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #316AC5;
                }

                .ui-datepicker .ui-datepicker-other-month a {
                    color: #ccc;
                }

                /* Hide the default jQuery UI icons and use custom arrows */
                .ui-datepicker .ui-icon {
                    display: none;
                }

                .ui-datepicker .ui-datepicker-prev:before {
                    content: "◀";
                    font-size: 8px;
                }

                .ui-datepicker .ui-datepicker-next:before {
                    content: "▶";
                    font-size: 8px;
                }

        </style>
        
    }


    @section PageScripts {


        <!-- Define MyScript object to prevent errors -->
        <script type="text/javascript">
            // Define MyScript object if it doesn't exist
            var MyScript = MyScript || {};

            // Add init method if it doesn't exist
            MyScript.init = MyScript.init || function(options) {
                console.log("MyScript.init called with options:", options);
                window.m_options = options; // Store options globally if needed
            };

            var MOSCI = MOSCI || {};
            MOSCI.MAX_ROWS = 19;

           

            // Function to validate duplicate offenders in the grid
            function validateDuplicateOffender(currentRowIndex, prefix, offenderId) {
                if (!prefix || !offenderId || offenderId.trim() === '') {
                    return { isValid: true, message: '' };
                }

                var combinedId = prefix + offenderId.trim();
                var duplicateFound = false;
                var duplicateRowIndex = -1;

                // Check all other rows for the same prefix + offender ID combination
                $('#inmateTable tbody tr').each(function(index) {
                    if (index !== currentRowIndex) {
                        var $row = $(this);
                        var rowPrefix = $row.find('select[id*="InmateIdPrefix"]').val() || '';
                        var rowOffenderId = $row.find('input[id*="OffenderId"]').val() || '';

                        if (rowPrefix && rowOffenderId.trim() !== '') {
                            var rowCombinedId = rowPrefix + rowOffenderId.trim();
                            if (rowCombinedId === combinedId) {
                                duplicateFound = true;
                                duplicateRowIndex = index + 1; // 1-based for user display
                                return false; // Break out of each loop
                            }
                        }
                    }
                });

                if (duplicateFound) {
                    return {
                        isValid: false,
                        message: `Duplicate offender found! Offender ${combinedId} already exists in row ${duplicateRowIndex}.`
                    };
                }

                return { isValid: true, message: '' };
            }

            // Function to show validation error message
            function showValidationError(message) {
                $('#ErrorMessage').text(message);
                $('#divErrorMessage').show();

                // Auto-hide after 5 seconds
                setTimeout(function() {
                    $('#divErrorMessage').hide();
                }, 5000);
            }

            // Function to hide validation error message
            function hideValidationError() {
                $('#divErrorMessage').hide();
            }

            // Auto-populate function for offender data - JavaScript-only implementation
            function autoPopulateOffender(inputElement, rowIndex, inmateIdPrefix) {
                var combinedOffenderId = inputElement.value.trim();
                console.log('=== Auto-populate triggered ===');
                console.log('Row Index:', rowIndex);
                console.log('Combined Offender ID:', combinedOffenderId);

                var $row = $('#inmateTable tbody tr').eq(rowIndex);

                if (!combinedOffenderId) {
                    // Clear fields if offender ID is empty
                    clearOffenderFields(rowIndex);
                    hideValidationError();
                    return;
                }

                var prefix = inmateIdPrefix || "";
                var offenderId = combinedOffenderId;

                //// If no prefix passed extract from combined ID input
                //if (!prefix && combinedOffenderId.length > 0 && /^[A-Za-z]/.test(combinedOffenderId[0])) {
                //    prefix = combinedOffenderId[0].toUpperCase();
                //    offenderId = combinedOffenderId.substring(1);
                //}
                //else { offenderId = combinedOffenderId; }

                // Validate for duplicates before proceeding
                var validation = validateDuplicateOffender(rowIndex, prefix, offenderId);
                if (!validation.isValid) {
                    showValidationError(validation.message);
                    // Clear the current row's offender ID to prevent duplicate
                    $row.find('input[id*="OffenderId"]').val('');
                    clearOffenderFields(rowIndex);
                    return;
                }

                // Hide any previous validation errors
                hideValidationError();

                // Update the prefix dropdown and hidden OffenderId field
                if (prefix) {
                    $row.find('select[id*="InmateIdPrefix"]').val(prefix);
                }
                $row.find('input[id*="OffenderId"]').val(offenderId);
                

                // Make AJAX call to get offender data
                $.ajax({
                    url: '@Url.Action("GetOffenderData", "Mosci", new { area = "Transfers" })',
                    type: 'POST',
                    data: {
                        prefix: prefix,
                        offenderId: offenderId
                    },
                    success: function(result) {
                        console.log('Auto-populate response:', result);

                        if (result.success && result.offender) {
                            var offender = result.offender;

                            // Populate the fields
                            $row.find('input[id*="LastName"]').val(offender.lastName || '');
                            $row.find('input[id*="FirstName"]').val(offender.firstName || '');

                            // Set From Institution if available
                            if (offender.frominsText) {
                                //$row.find('select[id*="FromInstitutionId"]').val(offender.fromInstitutionId);


                                $row.find('input[id*="frominsText"]').val(offender.frominsText || '');
                            }

                            console.log('Auto-populated:', offender.lastName, offender.firstName);
                        } else {
                            // Clear fields if no match found
                            $row.find('input[id*="LastName"]').val('');
                            $row.find('input[id*="FirstName"]').val('');
                            $row.find('input[id*="frominsText"]').val('');

                            console.log('No matching offender found');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Auto-populate error:', error);
                        // Clear fields on error
                        $row.find('input[id*="LastName"]').val('');
                        $row.find('input[id*="FirstName"]').val('');
                        $row.find('input[id*="frominsText"]').val('');
                    }
                });
            }

            

            $(document).on('change', '.auto-populate-field', function () {
                var $this = $(this);
                var rowIndex = $this.data('row-index') || $this.closest('tr').index();
                var $row = $this.closest('tr');
                var inmateIdPrefix = $row.find('select[id*="InmateIdPrefix"]').val() || '';

                console.log('Change event - rowIndex:', rowIndex, 'inmateIdPrefix:', inmateIdPrefix);
                autoPopulateOffender(this, rowIndex, inmateIdPrefix);
            });

            $(document).on('keydown', '.auto-populate-field', function (event) {
                if (event.keyCode == 13 || event.keyCode == 9) { // Enter or Tab
                    var $this = $(this);
                    var rowIndex = $this.data('row-index') || $this.closest('tr').index();
                    var $row = $this.closest('tr');
                    var inmateIdPrefix = $row.find('select[id*="InmateIdPrefix"]').val() || '';

                    console.log('Keydown event - rowIndex:', rowIndex, 'inmateIdPrefix:', inmateIdPrefix);
                    autoPopulateOffender(this, rowIndex, inmateIdPrefix);
                    return false;
                }
            });

            // Function to clear offender fields
            function clearOffenderFields(rowIndex) {
                var $row = $('#inmateTable tbody tr').eq(rowIndex);
                $row.find('input[id*="LastName"]').val('');
                $row.find('input[id*="FirstName"]').val('');
                $row.find('input[id*="frominsText"]').val('');
                //$row.find('input[id*="FromInstitutionId"]').val('');
                $row.find('select[id*="InmateIdPrefix"]').val('');
                $row.find('input[id*="OffenderId"]').val('');
            }


            
            // Function to clear the table except for one empty row
            MOSCI.clearTable = function() {
                $('#inmateTable tbody tr').not(':first').remove();
                var $firstRow = $('#inmateTable tbody tr:first');
                $firstRow.find('input[type="text"]').val('');
                $firstRow.find('select').prop('selectedIndex', 0);
                $firstRow.find('input[type="checkbox"]').prop('checked', false);
                $firstRow.find('input[type="hidden"]').val('');
            };
            // Function to update row indices and field names after adding/removing rows
            MOSCI.updateRowIndices = function () {
                $('#inmateTable tbody tr').each(function (index) {
                    var $row = $(this);

                    // Update all input field names and IDs to use the correct index
                    $row.find('input, select').each(function () {
                        var $field = $(this);
                        var name = $field.attr('name');
                        var id = $field.attr('id');

                        if (name && name.includes('Inmates[')) {
                            // Update the index in the name attribute
                            var newName = name.replace(/Inmates\[\d+\]/, 'Inmates[' + index + ']');
                            $field.attr('name', newName);
                        }

                        if (id && id.includes('Inmates_')) {
                            // Update the index in the id attribute
                            var newId = id.replace(/Inmates_\d+_/, 'Inmates_' + index + '_');
                            $field.attr('id', newId);
                        }
                    });

                    // Update data-row-index attribute for auto-populate fields
                    $row.find('.auto-populate-field').attr('data-row-index', index);
                });

                console.log('Updated row indices for', $('#inmateTable tbody tr').length, 'rows');
            };

            // Function to initialize datepickers
            MOSCI.initializeDatepickers = function () {
                // Initialize datepickers for all date inputs that don't already have it
                $('.datepicker-input').each(function () {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Initializing datepicker for input:', $input[0]);
                        try {
                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0, // Sunday
                                onSelect: function (dateText) {
                                    console.log('Date selected via datepicker:', dateText);
                                    $(this).trigger('change');
                                }
                            });

                            // Also add click handler to input itself
                            $input.on('focus click', function () {
                                console.log('Input focused/clicked, showing datepicker');
                                $(this).datepicker('show');
                            });

                            console.log('Datepicker initialized for input');
                        } catch (error) {
                            console.error('Error initializing datepicker:', error);
                        }
                    }
                });
            };                                                              

            // Handle calendar icon clicks with event delegation - simplified approach
            $(document).on('click', '.datepicker-trigger', function (e) {
                e.preventDefault();
                e.stopPropagation();

                var $trigger = $(this);
                var $input = $trigger.siblings('.datepicker-input');

                console.log('=== Calendar Icon Clicked ===');
                console.log('Trigger element:', $trigger[0]);
                console.log('Input found:', $input.length);
                console.log('Input element:', $input[0]);

                if ($input.length > 0) {
                    console.log('Input has datepicker class:', $input.hasClass('hasDatepicker'));

                    // Force initialize datepicker if not already done
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Force initializing datepicker...');
                        try {
                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0,
                                onSelect: function (dateText) {
                                    console.log('Date selected:', dateText);
                                    $(this).trigger('change');
                                }
                            });
                            console.log('Datepicker initialized successfully');
                        } catch (initError) {
                            console.error('Error initializing datepicker:', initError);
                            return;
                        }
                    }

                    // Try to show the datepicker
                    try {
                        console.log('Attempting to show datepicker...');
                        $input.datepicker('show');
                        console.log('Datepicker show command executed');
                    } catch (showError) {
                        console.error('Error showing datepicker:', showError);
                        // Alternative approach: focus the input which should trigger the datepicker
                        console.log('Trying alternative approach - focusing input');
                        $input.focus();

                        // If that doesn't work, try triggering a click event
                        setTimeout(function () {
                            $input.trigger('click');
                        }, 50);
                    }
                } else {
                    console.error('No input sibling found for calendar trigger');
                    console.log('Trigger parent:', $trigger.parent()[0]);
                    console.log('All inputs in parent:', $trigger.parent().find('input').length);
                }
            });
           


            $(function () {


                // Debug form submission
                $('#Mosci').on('submit', function (e) {
                    console.log('Form submission detected');
                    var formData = $(this).serialize();
                    console.log('Form data:', formData);

                    // Check if this is a search submission - look for the clicked button
                    var submitAction = $(document.activeElement).val() || $('button[type="submit"]:focus').val();
                    console.log('Submit action:', submitAction);

                    // Log search-specific values
                    var searchPrefix = $('#searchPrefixDropdown').val();
                    var searchOffenderId = $('#txtInmateNum').val();
                    console.log('Search values - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);

                    if (submitAction === 'Search') {
                        console.log('Search form submission detected');
                        console.log('Final search values being submitted - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);
                    } else if (submitAction === 'Save') {
                        console.log('=== SAVE FORM SUBMISSION DETECTED ===');

                        // Log all table rows and their data
                        var rowCount = $('#inmateTable tbody tr').length;
                        console.log('Total rows in table:', rowCount);

                        $('#inmateTable tbody tr').each(function (index) {
                            var $row = $(this);
                            var rowData = {
                                index: index,
                                //recno: $row.find('input[id*="Recno"]').val(),
                                prefix: $row.find('select[id*="InmateIdPrefix"]').val(),
                                offenderId: $row.find('input[id*="OffenderId"]').val(),
                                //combinedOffenderId: $row.find('input[id*="CombinedOffenderId"]').val(),
                                lastName: $row.find('input[id*="LastName"]').val(),
                                firstName: $row.find('input[id*="FirstName"]').val(),
                                fromInstitutionId: $row.find('input[id*="frominsText"]').val(),
                                //toInstitutionId: $row.find('select[id*="ToInstitutionId"]').val(),
                                schDate: $row.find('input[id*="SchDate"]').val(),
                                descrl: $row.find('input[id*="Descrl"]').val(),
                                isMarkedForRemoval: $row.find('input[id*="IsMarkedForRemoval"]:checked').length > 0,
                                isMarkedForDeletion: $row.find('input[id*="IsMarkedForDeletion"]:checked').length > 0
                            };
                            console.log('Row ' + index + ' data:', rowData);
                        });

                        // Log form field names to verify proper MVC binding
                        console.log('=== Form field names for MVC binding ===');
                        $('#inmateTable tbody tr').each(function (index) {
                            var $row = $(this);
                            $row.find('input, select').each(function () {
                                var $field = $(this);
                                var name = $field.attr('name');
                                var id = $field.attr('id');
                                var value = $field.val();
                                if (name && name.includes('Inmates[')) {
                                    console.log('Field:', name, 'ID:', id, 'Value:', value);
                                }
                            });
                        });
                    }
                });

            // Clone first row and clear inputs when adding a new inmate
            $('#btnAddNewInmate').on('click', function (e) {
                e.preventDefault();

                // Check if we've reached the maximum number of rows
                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
                    return null;
                }

                // Clone the template row
                var $newRow = $('#inmate-row-template').clone();

                // Generate a unique ID for the new row
                var rowId = 'inmate-row-' + new Date().getTime();
                $newRow.attr('id', rowId);

                // Clear all input values
                $newRow.find('input[type="text"]').val('');
                $newRow.find('select').prop('selectedIndex', 0);
                $newRow.find('input[type="checkbox"]').prop('checked', false);

                // Append the new row to the table
                $('#inmateTable tbody').append($newRow);

                // Scroll to the new row
                $('html, body').animate({
                    scrollTop: $newRow.offset().top - 100
                }, 500);

                $newRow.find('.datepicker-input').each(function () {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: true,
                            changeYear: true,
                            showButtonPanel: false,
                            yearRange: '-10:+10',
                            showOtherMonths: true,
                            selectOtherMonths: false,
                            firstDay: 0 // Sunday
                        });
                    }
                });
                MOSCI.updateRowIndices();
            });

            // Wait a bit for jQuery UI to be fully loaded
            setTimeout(function () {
                console.log('=== Initializing Datepickers ===');
                console.log('Found datepicker inputs:', $('.datepicker-input').length);

                // Initialize datepickers for existing rows
                MOSCI.initializeDatepickers();

                // Verify initialization
                setTimeout(function () {
                    console.log('Datepicker inputs with hasDatepicker class:', $('.datepicker-input.hasDatepicker').length);

                    // Test direct click on first input
                    var $firstInput = $('.datepicker-input').first();
                    if ($firstInput.length > 0) {
                        console.log('First input element:', $firstInput[0]);
                        console.log('First input has datepicker:', $firstInput.hasClass('hasDatepicker'));
                    }
                }, 200);
            }, 100);

            // Handle remove inmate button - using submitAction value instead of name
            $('button[value="RemoveSelected"]').on('click', function(e) {
                e.preventDefault();

                var hasChecked = false;
                var checkedCount = 0;
                var totalRows = $('#inmateTable tbody tr').length;

                // Count checked rows first
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                        checkedCount++;
                    }
                });

                if (checkedCount === 0) {
                    alert('Please select at least one inmate to remove.');
                    return;
                }

                // Check if removing would leave at least one row
                if (totalRows - checkedCount < 1) {
                    alert('Cannot remove all rows. At least one row must remain in the table.');
                    return;
                }

                //confirmation dialog
                if (!confirm("Are you sure you want to remove selected inmates")) { return; }

                // Remove the checked rows
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                        $row.remove();
                        hasChecked = true;
                    }
                });

                if (hasChecked) {
                    alert('Selected inmate(s) removed successfully.');
                }

                updateButtonState();
            });

            // Handle delete button - using AJAX calls for individual record deletion
            $('button[value="DeleteSelected"]').on('click', function(e) {
                e.preventDefault();

                var checkedRows = [];
                var totalRows = $('#inmateTable tbody tr').length;

                // Collect all checked rows with their data
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForDeletion"]').is(':checked')) {
                        var prefix = $row.find('select[id*="InmateIdPrefix"]').val();
                        var offenderId = $row.find('input[id*="OffenderId"]').val();

                        // Only include rows with valid data
                        if (prefix && offenderId && offenderId.trim() !== '') {
                            checkedRows.push({
                                row: $row,
                                prefix: prefix,
                                offenderId: offenderId.trim()
                            });
                        }
                    }
                });

                // Validation: Check if any rows are selected
                if (checkedRows.length === 0) {
                    alert('Please select at least one inmate with valid data to delete.');
                    return;
                }

                // Show confirmation popup with exact text as requested
                if (!confirm('Are you sure you want to delete an inmate from MOSCI?')) {
                    // User clicked "No" - do nothing
                    return;
                }

                // User clicked "Yes" - proceed with deletion
                var deletionPromises = [];
                var successCount = 0;
                var errorCount = 0;
                var errorMessages = [];

                // Process each checked row
                checkedRows.forEach(function(rowData) {
                    // Create the data structure expected by MosciPageViewModel
                    var viewModelData = {
                        'Inmates[0].InmateIdPrefix': rowData.prefix,
                        'Inmates[0].OffenderId': rowData.offenderId,
                        'Inmates[0].IsMarkedForDeletion': true
                    };

                    var promise = $.ajax({
                        url: '@Url.Action("DeleteMosciRecord", "Mosci", new { area = "Transfers" })',
                        type: 'POST',
                        data: viewModelData

                    }).done(function(response) {
                        if (response.success) {
                            // Clear the row (make it blank but keep the structure)
                            rowData.row.find('input[type="text"]').val('');
                            rowData.row.find('select').prop('selectedIndex', 0);
                            rowData.row.find('input[type="checkbox"]').prop('checked', false);
                            rowData.row.find('input[type="hidden"]').val('');
                            successCount++;
                        } else {
                            errorCount++;
                            errorMessages.push(response.message || 'Unknown error occurred');
                        }
                    }).fail(function(xhr, status, error) {
                        errorCount++;
                        errorMessages.push('Network error: ' + error);
                    });

                    deletionPromises.push(promise);
                });

                // Wait for all deletion requests to complete
                $.when.apply($, deletionPromises).always(function() {
                    // Show results to user
                    if (successCount > 0 && errorCount === 0) {
                        // All deletions successful
                        console.log('All selected inmates deleted successfully from MOSCI.');
                    } else if (successCount > 0 && errorCount > 0) {
                        // Some successful, some failed
                        alert('Some deletions completed successfully, but ' + errorCount + ' failed:\n' + errorMessages.join('\n'));
                    } else if (errorCount > 0) {
                        // All failed
                        alert('Failed to delete inmates:\n' + errorMessages.join('\n'));
                    }

                    updateButtonState();
                });
            });



            // Handle cancel button
            $('#btnCancel').on('click', function() {
                if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                    window.location.href = '@Url.Action("Index", "Home", new { area = "" })';
                }
            });


            var updateButtonState = function () {
                var rowCount = $('#inmateTable tbody tr').length;
                $('#btnAddNewInmate').prop('disabled', rowCount >= MOSCI.MAX_ROWS);
                console.log('Row count: ' + rowCount + ', Add button ' + (rowCount >= MOSCI.MAX_ROWS ? 'disabled' : 'enabled'));
            };


            updateButtonState();

            // Initialize numeric validation for existing rows
            $('.onlyNumeric').on('keypress', function(e) {
                // Allow only numbers (0-9)
                if (e.which < 48 || e.which > 57) {
                    e.preventDefault();
                }
            });

            // Add event handler for prefix dropdown changes to update combined offender ID
            $(document).on('change', 'select[id*="InmateIdPrefix"]', function() {
                var $row = $(this).closest('tr');
                var prefix = $(this).val();
                var offenderId = $row.find('input[id*="OffenderId"]').val();
                var rowIndex = $row.index();

                // Validate for duplicates when prefix changes
                if (prefix && offenderId && offenderId.trim() !== '') {
                    var validation = validateDuplicateOffender(rowIndex, prefix, offenderId.trim());
                    if (!validation.isValid) {
                        showValidationError(validation.message);
                        // Reset the prefix to empty to prevent duplicate
                        $(this).val('');
                        return;
                    } else {
                        hideValidationError();
                    }
                }

                var combinedId = prefix && offenderId ? prefix + offenderId : '';
                $row.find('input[id*="CombinedOffenderId"]').val(combinedId);
            });



            @*MyScript.init({
                MOSCI: '@Url.Action("Mosci", "Mosci", new { area = "Transfers" })',
                    GetEmployeeInfoByOaksId: '@Url.Action("GetEmployeeInfoByOaksId", "Mosci", new { area = "Transfers" })'
                });*@


           

            // Enable Enter key to trigger search
            $("#txtInmateNum").keyup(function (event) {
                if (event.keyCode == 13) {
                    $("#btnFindOffender").click();
                }
            });
            $('.scheduled-date input').datepicker('destroy');
            // Attach datepicker to all scheduled date inputs
            $('.scheduled-date input').datepicker({
                format: 'mm/dd/yyyy',
                autoclose: true,
                todayHighlight: true
            });
           

            $('.scheduled-date .date-icon').on('click', function (e) {
                e.preventDefault();
                var $input = $(this).closest('.scheduled-date').find('input');
                $input.datepicker('show');
            });



                // Function to validate all rows for duplicates before saving
                function validateAllRowsForDuplicates() {
                    var duplicates = [];
                    var processedCombinations = {};

                    $('#inmateTable tbody tr').each(function(index) {
                        var $row = $(this);
                        var prefix = $row.find('select[id*="InmateIdPrefix"]').val();
                        var offenderId = $row.find('input[id*="OffenderId"]').val();

                        if (prefix && offenderId && offenderId.trim() !== '') {
                            var combinedId = prefix + offenderId.trim();

                            if (processedCombinations[combinedId]) {
                                duplicates.push({
                                    combinedId: combinedId,
                                    rows: [processedCombinations[combinedId], index + 1]
                                });
                            } else {
                                processedCombinations[combinedId] = index + 1;
                            }
                        }
                    });

                    return duplicates;
                }

                // Handle save button - ensure all data is properly prepared before submission
                $('#btnSave').on('click', function (e) {
                    console.log('=== SAVE BUTTON CLICKED ===');

                    // Check for duplicate offenders before saving
                    var duplicates = validateAllRowsForDuplicates();
                    if (duplicates.length > 0) {
                        var errorMessage = 'Cannot save: Duplicate offenders found!\n\n';
                        duplicates.forEach(function(duplicate) {
                            errorMessage += `Offender ${duplicate.combinedId} appears in rows ${duplicate.rows.join(' and ')}\n`;
                        });
                        errorMessage += '\nPlease remove duplicates before saving.';

                        showValidationError('Cannot save: Duplicate offenders found! Please remove duplicates before saving.');
                        alert(errorMessage);
                        e.preventDefault();
                        return false;
                    }

                    // Update all row indices to ensure proper MVC binding
                    //MOSCI.updateRowIndices();

                    // Validate that we have at least one row with meaningful data
                    var hasValidData = false;
                    $('#inmateTable tbody tr').each(function () {
                        var $row = $(this);

                        var offenderId = $row.find('input[id*="OffenderId"]').val();
                        //var prefix = $row.find('select[id*="InmateIdPrefix"]').val(),
                        //var lastName = $row.find('input[id*="LastName"]').val(),
                        //var firstName = $row.find('input[id*="FirstName"]').val(),
                        //var fromInstitutionId = $row.find('input[id*="frominsText"]').val(),
                        //var toInstitutionId = $row.find('select[id*="ToInstitutionId"]').val(),
                        //var schDat = $row.find('input[id*="SchDate"]').val(),

                        //if (offenderId && offenderId.trim() !== '' && lastName && lastName.trim() !== '' && firstName && firstName.trim() !== '' && schDat && schDat.trim() !== '') {
                        if (offenderId && offenderId.trim() !== '') {
                            hasValidData = true;
                            return false; // break out of each loop
                        }
                    });

                    if (!hasValidData) {
                        alert('Please add at least one inmate with an Offender ID and destination institution before saving.');
                        e.preventDefault();
                        return false;
                    }

                    // Hide any validation errors if we reach this point
                    hideValidationError();
                    console.log('Save validation passed, proceeding with form submission');
                    return true;
                });

        });
        </script>


        <script type="text/javascript" src="~/Areas/Rh/Scripts/Common.js"></script>
    }
}